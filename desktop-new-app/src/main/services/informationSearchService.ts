import { Database } from 'sqlite3'
import path from 'path'
import { app } from 'electron'

export interface InformationRecord {
  id: number
  s_no: string
  bank: string
  name_of_officer: string
  designation: string
  mobile_no: string
  email: string
  last_login: string
  created_at: string
  updated_at: string
}

export interface SearchFilters {
  searchTerm?: string
  bank?: string
  designation?: string
}

export interface PaginationOptions {
  page: number
  limit: number
}

export interface SearchResult {
  data: InformationRecord[]
  total: number
  page: number
  limit: number
  totalPages: number
}

class InformationSearchService {
  private db: Database | null = null
  private dbPath: string

  constructor() {
    // Use the converted SQLite database
    this.dbPath = path.join(app.getAppPath(), 'resources', 'information_search.db')
  }

  /**
   * Initialize database connection
   */
  private async initDatabase(): Promise<Database> {
    if (this.db) {
      return this.db
    }

    return new Promise((resolve, reject) => {
      this.db = new Database(this.dbPath, (err) => {
        if (err) {
          console.error('Failed to connect to information search database:', err)
          reject(err)
        } else {
          console.log('Connected to information search database')
          resolve(this.db!)
        }
      })
    })
  }

  /**
   * Search information records with filters and pagination
   */
  async searchRecords(
    filters: SearchFilters = {},
    pagination: PaginationOptions = { page: 1, limit: 50 }
  ): Promise<SearchResult> {
    const db = await this.initDatabase()

    const { searchTerm, bank, designation } = filters
    const { page, limit } = pagination
    const offset = (page - 1) * limit

    // Build WHERE clause
    const conditions: string[] = []
    const params: any[] = []

    if (searchTerm && searchTerm.trim()) {
      conditions.push(`(
        name_of_officer LIKE ? OR 
        bank LIKE ? OR 
        designation LIKE ? OR 
        email LIKE ? OR 
        mobile_no LIKE ?
      )`)
      const searchPattern = `%${searchTerm.trim()}%`
      params.push(searchPattern, searchPattern, searchPattern, searchPattern, searchPattern)
    }

    if (bank && bank.trim()) {
      conditions.push('bank LIKE ?')
      params.push(`%${bank.trim()}%`)
    }

    if (designation && designation.trim()) {
      conditions.push('designation LIKE ?')
      params.push(`%${designation.trim()}%`)
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM information_data ${whereClause}`
    const total = await new Promise<number>((resolve, reject) => {
      db.get(countQuery, params, (err, row: any) => {
        if (err) reject(err)
        else resolve(row.total)
      })
    })

    // Get paginated data
    const dataQuery = `
      SELECT * FROM information_data 
      ${whereClause} 
      ORDER BY bank, name_of_officer 
      LIMIT ? OFFSET ?
    `
    const dataParams = [...params, limit, offset]

    const data = await new Promise<InformationRecord[]>((resolve, reject) => {
      db.all(dataQuery, dataParams, (err, rows: any[]) => {
        if (err) reject(err)
        else resolve(rows as InformationRecord[])
      })
    })

    return {
      data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    }
  }

  /**
   * Get a single record by ID
   */
  async getRecordById(id: number): Promise<InformationRecord | null> {
    const db = await this.initDatabase()

    return new Promise((resolve, reject) => {
      db.get('SELECT * FROM information_data WHERE id = ?', [id], (err, row: any) => {
        if (err) reject(err)
        else resolve(row as InformationRecord || null)
      })
    })
  }

  /**
   * Create a new record
   */
  async createRecord(record: Omit<InformationRecord, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    const db = await this.initDatabase()

    const insertQuery = `
      INSERT INTO information_data (
        s_no, bank, name_of_officer, designation, mobile_no, email, last_login
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `

    return new Promise((resolve, reject) => {
      db.run(insertQuery, [
        record.s_no,
        record.bank,
        record.name_of_officer,
        record.designation,
        record.mobile_no,
        record.email,
        record.last_login
      ], function(err) {
        if (err) reject(err)
        else resolve(this.lastID)
      })
    })
  }

  /**
   * Update an existing record
   */
  async updateRecord(id: number, record: Partial<Omit<InformationRecord, 'id' | 'created_at' | 'updated_at'>>): Promise<boolean> {
    const db = await this.initDatabase()

    const fields = Object.keys(record).filter(key => record[key as keyof typeof record] !== undefined)
    const setClause = fields.map(field => `${field} = ?`).join(', ')
    const values = fields.map(field => record[field as keyof typeof record])

    const updateQuery = `
      UPDATE information_data 
      SET ${setClause}, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `

    return new Promise((resolve, reject) => {
      db.run(updateQuery, [...values, id], function(err) {
        if (err) reject(err)
        else resolve(this.changes > 0)
      })
    })
  }

  /**
   * Delete a record
   */
  async deleteRecord(id: number): Promise<boolean> {
    const db = await this.initDatabase()

    return new Promise((resolve, reject) => {
      db.run('DELETE FROM information_data WHERE id = ?', [id], function(err) {
        if (err) reject(err)
        else resolve(this.changes > 0)
      })
    })
  }

  /**
   * Get unique banks for filter dropdown
   */
  async getUniqueBanks(): Promise<string[]> {
    const db = await this.initDatabase()

    return new Promise((resolve, reject) => {
      db.all('SELECT DISTINCT bank FROM information_data WHERE bank IS NOT NULL AND bank != "" ORDER BY bank', [], (err, rows: any[]) => {
        if (err) reject(err)
        else resolve(rows.map(row => row.bank))
      })
    })
  }

  /**
   * Get unique designations for filter dropdown
   */
  async getUniqueDesignations(): Promise<string[]> {
    const db = await this.initDatabase()

    return new Promise((resolve, reject) => {
      db.all('SELECT DISTINCT designation FROM information_data WHERE designation IS NOT NULL AND designation != "" ORDER BY designation', [], (err, rows: any[]) => {
        if (err) reject(err)
        else resolve(rows.map(row => row.designation))
      })
    })
  }

  /**
   * Close database connection
   */
  async close(): Promise<void> {
    if (this.db) {
      return new Promise((resolve, reject) => {
        this.db!.close((err) => {
          if (err) reject(err)
          else {
            this.db = null
            resolve()
          }
        })
      })
    }
  }
}

// Export singleton instance
export const informationSearchService = new InformationSearchService()
