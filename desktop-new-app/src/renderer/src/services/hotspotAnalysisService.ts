// Hotspot Analysis Service for Fraud Investigation
import { ComplaintData } from '../../../shared/api'

interface GeographicPoint {
  latitude: number
  longitude: number
  weight: number // Transaction amount or count
  metadata: {
    complaintId?: string
    transactionId?: string
    nodeId?: string
    bank?: string
    transaction?: {
      fraud_type?: string
      receiver_bank?: string
      [key: string]: unknown
    }
    [key: string]: unknown
  }
}

interface Hotspot {
  id: string
  center: { latitude: number; longitude: number }
  radius: number // in kilometers
  intensity: number // 0-1 scale
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  transactionCount: number
  totalAmount: number
  locations: GeographicPoint[]
  analysis: {
    averageAmount: number
    maxAmount: number
    timeSpan: string
    fraudTypes: string[]
    bankCount: number
  }
}

interface HotspotAnalysisResult {
  hotspots: Hotspot[]
  statistics: {
    totalHotspots: number
    criticalHotspots: number
    highRiskHotspots: number
    totalAmountInHotspots: number
    averageHotspotIntensity: number
  }
  recommendations: string[]
}

class HotspotAnalysisService {
  private readonly EARTH_RADIUS_KM = 6371
  private readonly MIN_POINTS_FOR_HOTSPOT = 3
  private readonly DEFAULT_RADIUS_KM = 50 // 50km radius for clustering

  /**
   * Perform comprehensive hotspot analysis on complaint data
   */
  async analyzeHotspots(
    complaints: ComplaintData[],
    options: {
      radiusKm?: number
      minPointsForHotspot?: number
      analysisType?: 'bank' | 'fraudster' | 'combined'
    } = {}
  ): Promise<HotspotAnalysisResult> {
    const {
      radiusKm = this.DEFAULT_RADIUS_KM,
      minPointsForHotspot = this.MIN_POINTS_FOR_HOTSPOT,
      analysisType = 'combined'
    } = options

    try {
      // Extract geographic points from complaints
      const points = await this.extractGeographicPoints(complaints, analysisType)

      if (points.length < minPointsForHotspot) {
        return this.createEmptyResult()
      }

      // Perform clustering to identify hotspots
      const clusters = this.performDBSCANClustering(points, radiusKm, minPointsForHotspot)

      // Convert clusters to hotspots with analysis
      const hotspots = await this.convertClustersToHotspots(clusters, complaints)

      // Calculate statistics
      const statistics = this.calculateStatistics(hotspots)

      // Generate recommendations
      const recommendations = this.generateRecommendations(hotspots, statistics)

      return {
        hotspots,
        statistics,
        recommendations
      }
    } catch (error) {
      console.error('Error in hotspot analysis:', error)
      return this.createEmptyResult()
    }
  }

  /**
   * Extract geographic points from complaint data
   */
  private async extractGeographicPoints(
    complaints: ComplaintData[],
    analysisType: 'bank' | 'fraudster' | 'combined'
  ): Promise<GeographicPoint[]> {
    const points: GeographicPoint[] = []

    for (const complaint of complaints) {
      if (analysisType === 'bank' || analysisType === 'combined') {
        // Extract bank locations from transactions
        const bankPoints = await this.extractBankPoints(complaint)
        points.push(...bankPoints)
      }

      if (analysisType === 'fraudster' || analysisType === 'combined') {
        // Extract fraudster locations from graph data
        const fraudsterPoints = await this.extractFraudsterPoints(complaint)
        points.push(...fraudsterPoints)
      }
    }

    return points
  }

  /**
   * Extract bank location points from complaint transactions
   */
  private async extractBankPoints(complaint: ComplaintData): Promise<GeographicPoint[]> {
    const points: GeographicPoint[] = []

    if (!complaint.layer_transactions) return points

    // Process all layer transactions
    for (const [layer, transactions] of Object.entries(complaint.layer_transactions)) {
      if (Array.isArray(transactions)) {
        for (const transaction of transactions) {
          if (transaction.receiver_ifsc) {
            // Mock coordinates for now - in real implementation, use geocoded data
            const coords = this.getMockCoordinatesForIFSC(transaction.receiver_ifsc)
            if (coords) {
              points.push({
                latitude: coords.lat,
                longitude: coords.lng,
                weight: parseFloat(transaction.amount?.replace(/[,]/g, '') || '0'),
                metadata: {
                  type: 'bank',
                  ifsc: transaction.receiver_ifsc,
                  bank: transaction.receiver_bank,
                  transaction: transaction,
                  layer: parseInt(layer.replace('Layer ', ''))
                }
              })
            }
          }
        }
      }
    }

    return points
  }

  /**
   * Extract fraudster location points from graph data
   */
  private async extractFraudsterPoints(complaint: ComplaintData): Promise<GeographicPoint[]> {
    const points: GeographicPoint[] = []

    if (!complaint.graph_data?.nodes) return points

    for (const node of complaint.graph_data.nodes) {
      // Extract address and try to geocode
      const address = this.extractAddressFromNode(node)
      if (address) {
        // Mock coordinates for now - in real implementation, use geocoded data
        const coords = this.getMockCoordinatesForAddress(address)
        if (coords) {
          points.push({
            latitude: coords.lat,
            longitude: coords.lng,
            weight: parseFloat(node.data.amount?.toString().replace(/[,]/g, '') || '0'),
            metadata: {
              type: 'fraudster',
              nodeId: node.id,
              address: address,
              layer: node.data.layer || 0,
              nodeData: node.data
            }
          })
        }
      }
    }

    return points
  }

  /**
   * Perform DBSCAN clustering to identify hotspots
   */
  private performDBSCANClustering(
    points: GeographicPoint[],
    radiusKm: number,
    minPoints: number
  ): GeographicPoint[][] {
    const clusters: GeographicPoint[][] = []
    const visited = new Set<number>()
    const clustered = new Set<number>()

    for (let i = 0; i < points.length; i++) {
      if (visited.has(i)) continue

      visited.add(i)
      const neighbors = this.getNeighbors(points, i, radiusKm)

      if (neighbors.length >= minPoints) {
        // Start a new cluster
        const cluster: GeographicPoint[] = []
        this.expandCluster(points, i, neighbors, cluster, visited, clustered, radiusKm, minPoints)
        if (cluster.length >= minPoints) {
          clusters.push(cluster)
        }
      }
    }

    return clusters
  }

  /**
   * Expand cluster using DBSCAN algorithm
   */
  private expandCluster(
    points: GeographicPoint[],
    pointIndex: number,
    neighbors: number[],
    cluster: GeographicPoint[],
    visited: Set<number>,
    clustered: Set<number>,
    radiusKm: number,
    minPoints: number
  ): void {
    cluster.push(points[pointIndex])
    clustered.add(pointIndex)

    for (let i = 0; i < neighbors.length; i++) {
      const neighborIndex = neighbors[i]

      if (!visited.has(neighborIndex)) {
        visited.add(neighborIndex)
        const neighborNeighbors = this.getNeighbors(points, neighborIndex, radiusKm)

        if (neighborNeighbors.length >= minPoints) {
          neighbors.push(...neighborNeighbors.filter((n) => !neighbors.includes(n)))
        }
      }

      if (!clustered.has(neighborIndex)) {
        cluster.push(points[neighborIndex])
        clustered.add(neighborIndex)
      }
    }
  }

  /**
   * Get neighbors within radius
   */
  private getNeighbors(points: GeographicPoint[], pointIndex: number, radiusKm: number): number[] {
    const neighbors: number[] = []
    const point = points[pointIndex]

    for (let i = 0; i < points.length; i++) {
      if (i === pointIndex) continue

      const distance = this.calculateDistance(
        point.latitude,
        point.longitude,
        points[i].latitude,
        points[i].longitude
      )

      if (distance <= radiusKm) {
        neighbors.push(i)
      }
    }

    return neighbors
  }

  /**
   * Calculate distance between two points using Haversine formula
   */
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const dLat = this.toRadians(lat2 - lat1)
    const dLon = this.toRadians(lon2 - lon1)

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2)

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    return this.EARTH_RADIUS_KM * c
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180)
  }

  /**
   * Convert clusters to hotspots with detailed analysis
   */
  private async convertClustersToHotspots(
    clusters: GeographicPoint[][],
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _complaints: ComplaintData[]
  ): Promise<Hotspot[]> {
    const hotspots: Hotspot[] = []

    for (let i = 0; i < clusters.length; i++) {
      const cluster = clusters[i]
      const center = this.calculateClusterCenter(cluster)
      const radius = this.calculateClusterRadius(cluster, center)

      const totalAmount = cluster.reduce((sum, point) => sum + point.weight, 0)
      const transactionCount = cluster.length
      const averageAmount = totalAmount / transactionCount
      const maxAmount = Math.max(...cluster.map((p) => p.weight))

      // Determine risk level
      const riskLevel = this.determineRiskLevel(totalAmount, transactionCount, averageAmount)

      // Calculate intensity (0-1 scale)
      const intensity = Math.min(totalAmount / 1000000, 1) // Normalize to 1M

      // Extract fraud types and banks
      const fraudTypes = [
        ...new Set(
          cluster.map((p) => p.metadata.transaction?.fraud_type || 'unknown').filter(Boolean)
        )
      ]
      const banks = [
        ...new Set(
          cluster
            .map((p) => p.metadata.bank || p.metadata.transaction?.receiver_bank)
            .filter(Boolean)
        )
      ]

      hotspots.push({
        id: `hotspot_${i + 1}`,
        center,
        radius,
        intensity,
        riskLevel,
        transactionCount,
        totalAmount,
        locations: cluster,
        analysis: {
          averageAmount,
          maxAmount,
          timeSpan: this.calculateTimeSpan(cluster),
          fraudTypes,
          bankCount: banks.length
        }
      })
    }

    return hotspots.sort((a, b) => b.intensity - a.intensity) // Sort by intensity
  }

  /**
   * Calculate cluster center (centroid)
   */
  private calculateClusterCenter(cluster: GeographicPoint[]): {
    latitude: number
    longitude: number
  } {
    const totalLat = cluster.reduce((sum, point) => sum + point.latitude, 0)
    const totalLng = cluster.reduce((sum, point) => sum + point.longitude, 0)

    return {
      latitude: totalLat / cluster.length,
      longitude: totalLng / cluster.length
    }
  }

  /**
   * Calculate cluster radius
   */
  private calculateClusterRadius(
    cluster: GeographicPoint[],
    center: { latitude: number; longitude: number }
  ): number {
    const distances = cluster.map((point) =>
      this.calculateDistance(center.latitude, center.longitude, point.latitude, point.longitude)
    )

    return Math.max(...distances)
  }

  /**
   * Determine risk level based on metrics
   */
  private determineRiskLevel(
    totalAmount: number,
    transactionCount: number,
    averageAmount: number
  ): 'low' | 'medium' | 'high' | 'critical' {
    if (totalAmount > 5000000 || transactionCount > 50 || averageAmount > 200000) {
      return 'critical'
    } else if (totalAmount > 2000000 || transactionCount > 20 || averageAmount > 100000) {
      return 'high'
    } else if (totalAmount > 500000 || transactionCount > 10 || averageAmount > 50000) {
      return 'medium'
    } else {
      return 'low'
    }
  }

  /**
   * Calculate statistics for all hotspots
   */
  private calculateStatistics(hotspots: Hotspot[]): {
    totalHotspots: number
    criticalHotspots: number
    highRiskHotspots: number
    totalAmountInHotspots: number
    averageHotspotIntensity: number
  } {
    return {
      totalHotspots: hotspots.length,
      criticalHotspots: hotspots.filter((h) => h.riskLevel === 'critical').length,
      highRiskHotspots: hotspots.filter((h) => h.riskLevel === 'high' || h.riskLevel === 'critical')
        .length,
      totalAmountInHotspots: hotspots.reduce((sum, h) => sum + h.totalAmount, 0),
      averageHotspotIntensity:
        hotspots.reduce((sum, h) => sum + h.intensity, 0) / (hotspots.length || 1)
    }
  }

  /**
   * Generate recommendations based on hotspot analysis
   */
  private generateRecommendations(
    hotspots: Hotspot[],
    statistics: {
      totalHotspots: number
      criticalHotspots: number
      highRiskHotspots: number
      totalAmountInHotspots: number
      averageHotspotIntensity: number
    }
  ): string[] {
    const recommendations: string[] = []

    if (statistics.criticalHotspots > 0) {
      recommendations.push(
        `Immediate attention required: ${statistics.criticalHotspots} critical fraud hotspots detected`
      )
    }

    if (statistics.highRiskHotspots > 3) {
      recommendations.push('Consider deploying additional monitoring resources to high-risk areas')
    }

    const topHotspot = hotspots[0]
    if (topHotspot && topHotspot.intensity > 0.7) {
      recommendations.push(
        `Focus investigation on primary hotspot with ${topHotspot.transactionCount} transactions`
      )
    }

    if (statistics.averageHotspotIntensity > 0.5) {
      recommendations.push(
        'Pattern suggests organized fraud network - coordinate multi-jurisdictional response'
      )
    }

    return recommendations
  }

  // Helper methods for mock data
  private getMockCoordinatesForIFSC(ifsc: string): { lat: number; lng: number } | null {
    // Mock implementation - in real app, use actual geocoded data
    const hash = this.simpleHash(ifsc)
    return {
      lat: 20 + (hash % 15), // Spread across India
      lng: 72 + (hash % 20)
    }
  }

  private getMockCoordinatesForAddress(address: string): { lat: number; lng: number } | null {
    // Mock implementation - in real app, use actual geocoded data
    const hash = this.simpleHash(address)
    return {
      lat: 20 + (hash % 15),
      lng: 72 + (hash % 20)
    }
  }

  private extractAddressFromNode(node: { data: Record<string, unknown> }): string | null {
    const addressFields = ['address', 'account_holder_address', 'location', 'city']
    for (const field of addressFields) {
      if (node.data[field] && typeof node.data[field] === 'string') {
        return node.data[field]
      }
    }
    return null
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private calculateTimeSpan(_cluster: GeographicPoint[]): string {
    // Mock implementation - in real app, calculate from transaction dates
    return '30 days'
  }

  private simpleHash(str: string): number {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash)
  }

  private createEmptyResult(): HotspotAnalysisResult {
    return {
      hotspots: [],
      statistics: {
        totalHotspots: 0,
        criticalHotspots: 0,
        highRiskHotspots: 0,
        totalAmountInHotspots: 0,
        averageHotspotIntensity: 0
      },
      recommendations: ['Insufficient data for hotspot analysis']
    }
  }
}

// Export singleton instance
export const hotspotAnalysisService = new HotspotAnalysisService()
export type { Hotspot, HotspotAnalysisResult }
