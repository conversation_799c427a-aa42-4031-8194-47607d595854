import React, { useState, useEffect, useCallback } from 'react'
import { ComplaintData, TransactionData } from '../../../shared/api'
import UnifiedNavbar from '../components/UnifiedNavbar'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import { Button } from '../components/ui/Button'
import { useThemeContext } from '../context/useThemeContext'
import { cn } from '../lib/aceternity-utils'
import { motion, AnimatePresence } from 'motion/react'
import {
  FullWidthLayout,
  ResponsiveGrid,
  FullWidthSection
} from '../components/layout/FullWidthLayout'

interface IFSCData {
  ifsc: string
  bank: string
  branch: string
  city: string
  state: string
  district: string
  address: string
  contact?: string
  micr?: string
  swift?: string
  rtgs: boolean
  neft: boolean
  imps: boolean
  upi: boolean
  count: number
}

interface StateData {
  state: string
  count: number
  banks: Set<string>
  districts: Set<string>
  totalAmount: number
}

interface CommonAccount {
  account: string
  count: number
  complaints: string[]
  ifsc?: string
  bank?: string
  location?: {
    city?: string
    state?: string
    district?: string
    address?: string
  }
}

interface AccountHolder {
  account: string
  name?: string
  address?: string
  city?: string
  state?: string
  district?: string
  pincode?: string
}

const AllStatsPage: React.FC = () => {
  const { isDark } = useThemeContext()
  const [complaints, setComplaints] = useState<ComplaintData[]>([])
  const [ifscData, setIfscData] = useState<Map<string, IFSCData>>(new Map())
  const [stateData, setStateData] = useState<Map<string, StateData>>(new Map())
  const [loading, setLoading] = useState(true)
  const [scanning, setScanning] = useState(false)
  const [scanProgress, setScanProgress] = useState(0)
  const [currentScanningIFSC, setCurrentScanningIFSC] = useState<string>('')

  // Common account analysis state - only keep common account analysis
  const [selectedComplaints, setSelectedComplaints] = useState<string[]>([])
  const [commonAccounts, setCommonAccounts] = useState<CommonAccount[]>([])
  const [, setAccountHolders] = useState<Map<string, AccountHolder>>(new Map())
  const [analysisLoading, setAnalysisLoading] = useState(false)
  const [complaintSearchQuery, setComplaintSearchQuery] = useState<string>('')

  const fetchComplaints = useCallback(async (): Promise<void> => {
    try {
      setLoading(true)
      const dbComplaints: ComplaintData[] = await window.api.database.getComplaints()
      setComplaints(dbComplaints)
    } catch (error) {
      console.error('Error fetching complaints:', error)
    } finally {
      setLoading(false)
    }
  }, [])

  const extractIFSCCodes = useCallback((complaints: ComplaintData[]): string[] => {
    const ifscCodes = new Set<string>()

    complaints.forEach((complaint) => {
      // Extract from layer_transactions
      if (complaint.layer_transactions) {
        Object.values(complaint.layer_transactions).forEach((layer) => {
          if (Array.isArray(layer)) {
            layer.forEach((transaction) => {
              if (transaction.receiver_ifsc) {
                ifscCodes.add(transaction.receiver_ifsc)
              }
              if (transaction.sender_ifsc) {
                ifscCodes.add(transaction.sender_ifsc)
              }
            })
          }
        })
      }

      // Extract from bank_notice_data
      if (
        complaint.bank_notice_data?.suspects &&
        Array.isArray(complaint.bank_notice_data.suspects)
      ) {
        complaint.bank_notice_data.suspects.forEach((suspect: { ifsc_code?: string }) => {
          if (suspect.ifsc_code) {
            ifscCodes.add(suspect.ifsc_code)
          }
        })
      }
    })

    return Array.from(ifscCodes).filter((code) => code && code.length === 11)
  }, [])

  const scanIFSCCodes = useCallback(async (ifscCodes: string[]): Promise<void> => {
    if (ifscCodes.length === 0) return

    setScanning(true)
    setScanProgress(0)

    const newIfscData = new Map<string, IFSCData>()
    const newStateData = new Map<string, StateData>()

    for (let i = 0; i < ifscCodes.length; i++) {
      const ifsc = ifscCodes[i]
      setCurrentScanningIFSC(ifsc)
      setScanProgress((i / ifscCodes.length) * 100)

      try {
        const data = await window.api.ifsc.fetchDetails(ifsc)

        if (data) {
          const ifscInfo: IFSCData = {
            ifsc: ifsc,
            bank: data.BANK || 'Unknown',
            branch: data.BRANCH || 'Unknown',
            city: data.CITY || 'Unknown',
            state: data.STATE || 'Unknown',
            district: data.DISTRICT || 'Unknown',
            address: data.ADDRESS || 'Unknown',
            contact: data.CONTACT,
            micr: data.MICR,
            swift: data.SWIFT,
            rtgs: data.RTGS || false,
            neft: data.NEFT || false,
            imps: data.IMPS || false,
            upi: data.UPI || false,
            count: 1
          }

          // Update IFSC data
          if (newIfscData.has(ifsc)) {
            const existing = newIfscData.get(ifsc)!
            existing.count += 1
            newIfscData.set(ifsc, existing)
          } else {
            newIfscData.set(ifsc, ifscInfo)
          }

          // Update state data
          const state = data.STATE || 'Unknown'
          if (newStateData.has(state)) {
            const existing = newStateData.get(state)!
            existing.count += 1
            existing.banks.add(data.BANK || 'Unknown')
            existing.districts.add(data.DISTRICT || 'Unknown')
          } else {
            newStateData.set(state, {
              state: state,
              count: 1,
              banks: new Set([data.BANK || 'Unknown']),
              districts: new Set([data.DISTRICT || 'Unknown']),
              totalAmount: 0
            })
          }
        }
      } catch (error) {
        console.error(`Error fetching IFSC details for ${ifsc}:`, error)
      }

      // Small delay to show animation
      await new Promise((resolve) => setTimeout(resolve, 100))
    }

    setIfscData(newIfscData)
    setStateData(newStateData)
    setScanProgress(100)
    setScanning(false)
    setCurrentScanningIFSC('')
  }, [])

  const startScan = useCallback(async (): Promise<void> => {
    const ifscCodes = extractIFSCCodes(complaints)
    await scanIFSCCodes(ifscCodes)
  }, [complaints, extractIFSCCodes, scanIFSCCodes])

  useEffect(() => {
    fetchComplaints()
  }, [fetchComplaints])

  // Common account analysis functions
  const handleAnalyzeCommonAccounts = useCallback(async (): Promise<void> => {
    setAnalysisLoading(true)
    try {
      const selectedComplaintData = complaints.filter((c) => selectedComplaints.includes(c.id))
      const accountMap: Record<
        string,
        { count: number; complaints: string[]; ifsc?: string; transactions: TransactionData[] }
      > = {}

      selectedComplaintData.forEach((complaint) => {
        if (complaint.layer_transactions) {
          Object.values(complaint.layer_transactions)
            .flat()
            .forEach((txn: TransactionData | unknown) => {
              if (txn && typeof txn === 'object' && 'receiver_account' in txn) {
                const transaction = txn as TransactionData
                const account = transaction.receiver_account // Only search receiver accounts
                const ifsc = transaction.receiver_ifsc

                if (account) {
                  if (!accountMap[account]) {
                    accountMap[account] = { count: 0, complaints: [], ifsc, transactions: [] }
                  }
                  accountMap[account].count++
                  accountMap[account].transactions.push(transaction)
                  if (!accountMap[account].complaints.includes(complaint.title)) {
                    accountMap[account].complaints.push(complaint.title)
                  }
                  if (ifsc && !accountMap[account].ifsc) {
                    accountMap[account].ifsc = ifsc
                  }
                }
              }
            })
        }
      })

      // Filter accounts that appear in multiple complaints (not just multiple times in same complaint)
      const commons = Object.entries(accountMap)
        .filter(([, v]) => v.complaints.length > 1)
        .map(([account, v]) => ({
          account,
          count: v.count,
          complaints: v.complaints,
          ifsc: v.ifsc
        }))

      // Fetch IFSC details for location information
      const enrichedCommons = await Promise.all(
        commons.map(async (common) => {
          if (common.ifsc) {
            try {
              const ifscResult = await window.api.ifsc.fetchDetails(common.ifsc)
              if (ifscResult) {
                return {
                  ...common,
                  bank: ifscResult.BANK,
                  location: {
                    city: ifscResult.CITY,
                    state: ifscResult.STATE,
                    district: ifscResult.DISTRICT,
                    address: ifscResult.ADDRESS
                  }
                }
              }
            } catch (error) {
              console.error(`Error fetching IFSC details for ${common.ifsc}:`, error)
            }
          }
          return common
        })
      )

      setCommonAccounts(enrichedCommons)
    } catch (error) {
      console.error('Error analyzing common accounts:', error)
    } finally {
      setAnalysisLoading(false)
    }
  }, [complaints, selectedComplaints])

  // Update account holder information (currently unused but kept for future use)
  const updateAccountHolder = useCallback(
    (_account: string, _holderInfo: Partial<AccountHolder>): void => {
      setAccountHolders((prev) => {
        const updated = new Map(prev)
        const existing = updated.get(_account) || { account: _account }
        updated.set(_account, { ...existing, ..._holderInfo })
        return updated
      })
    },
    []
  )

  // Suppress unused variable warning by referencing it
  void updateAccountHolder

  const topStates = Array.from(stateData.values())
    .sort((a, b) => b.count - a.count)
    .slice(0, 10)

  const topBanks = Array.from(ifscData.values())
    .reduce(
      (acc, curr) => {
        const existing = acc.find((item) => item.bank === curr.bank)
        if (existing) {
          existing.count += curr.count
        } else {
          acc.push({ bank: curr.bank, count: curr.count })
        }
        return acc
      },
      [] as { bank: string; count: number }[]
    )
    .sort((a, b) => b.count - a.count)
    .slice(0, 10)

  if (loading) {
    return (
      <FullWidthLayout>
        <UnifiedNavbar title="All Statistics" showBackButton />
        <FullWidthSection>
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className={cn('text-lg', isDark ? 'text-white' : 'text-gray-900')}>
                Loading complaints...
              </p>
            </div>
          </div>
        </FullWidthSection>
      </FullWidthLayout>
    )
  }

  return (
    <FullWidthLayout>
      <UnifiedNavbar title="Common Account Analysis" showBackButton />

      {/* Geographical analysis section has been removed - keeping only common account analysis */}
      <FullWidthSection className="space-y-6">
        {/* Overview Cards */}
        <ResponsiveGrid columns={{ sm: 1, md: 2, lg: 4 }} className="gap-6">
          <Card
            className={cn(
              'backdrop-blur-md border-0 shadow-lg',
              isDark ? 'bg-white/10' : 'bg-white/80'
            )}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Complaints</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{complaints.length}</div>
            </CardContent>
          </Card>

          <Card
            className={cn(
              'backdrop-blur-md border-0 shadow-lg',
              isDark ? 'bg-white/10' : 'bg-white/80'
            )}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Unique IFSC Codes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{ifscData.size}</div>
            </CardContent>
          </Card>

          <Card
            className={cn(
              'backdrop-blur-md border-0 shadow-lg',
              isDark ? 'bg-white/10' : 'bg-white/80'
            )}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">States Covered</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stateData.size}</div>
            </CardContent>
          </Card>

          <Card
            className={cn(
              'backdrop-blur-md border-0 shadow-lg',
              isDark ? 'bg-white/10' : 'bg-white/80'
            )}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Scan Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {scanning ? 'Scanning...' : ifscData.size > 0 ? 'Complete' : 'Ready'}
              </div>
            </CardContent>
          </Card>
        </ResponsiveGrid>

        {/* Scan Control */}
        <Card
          className={cn(
            'backdrop-blur-md border-0 shadow-lg',
            isDark ? 'bg-white/10' : 'bg-white/80'
          )}
        >
          <CardHeader>
            <CardTitle>IFSC Code Scanner</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <p className="text-sm text-muted-foreground">
                Scan all IFSC codes from complaints to get geographic data
              </p>
              <Button
                onClick={startScan}
                disabled={scanning || complaints.length === 0}
                className="min-w-[120px]"
              >
                {scanning ? 'Scanning...' : 'Start Scan'}
              </Button>
            </div>

            <AnimatePresence>
              {scanning && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-2"
                >
                  <div className="flex items-center justify-between text-sm">
                    <span>Progress: {Math.round(scanProgress)}%</span>
                    <span className="font-mono">{currentScanningIFSC}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <motion.div
                      className="bg-blue-500 h-2 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: `${scanProgress}%` }}
                      transition={{ duration: 0.3 }}
                    />
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </CardContent>
        </Card>

        {/* Statistics Tables */}
        {ifscData.size > 0 && (
          <ResponsiveGrid columns={{ sm: 1, lg: 2 }} className="gap-6">
            {/* Top States */}
            <Card
              className={cn(
                'backdrop-blur-md border-0 shadow-lg',
                isDark ? 'bg-white/10' : 'bg-white/80'
              )}
            >
              <CardHeader>
                <CardTitle>Top States by IFSC Count</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {topStates.map((state, index) => (
                    <div key={state.state} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div
                          className={cn(
                            'w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold',
                            index < 3 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'
                          )}
                        >
                          {index + 1}
                        </div>
                        <div>
                          <p className="font-medium">{state.state}</p>
                          <p className="text-sm text-muted-foreground">
                            {state.banks.size} banks, {state.districts.size} districts
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold">{state.count}</p>
                        <p className="text-sm text-muted-foreground">IFSCs</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Top Banks */}
            <Card
              className={cn(
                'backdrop-blur-md border-0 shadow-lg',
                isDark ? 'bg-white/10' : 'bg-white/80'
              )}
            >
              <CardHeader>
                <CardTitle>Top Banks by IFSC Count</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {topBanks.map((bank, index) => (
                    <div key={bank.bank} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div
                          className={cn(
                            'w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold',
                            index < 3 ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-700'
                          )}
                        >
                          {index + 1}
                        </div>
                        <div>
                          <p className="font-medium">{bank.bank}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold">{bank.count}</p>
                        <p className="text-sm text-muted-foreground">IFSCs</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </ResponsiveGrid>
        )}

        {/* India Map Visualization */}
        {stateData.size > 0 && (
          <Card
            className={cn(
              'backdrop-blur-md border-0 shadow-lg',
              isDark ? 'bg-white/10' : 'bg-white/80'
            )}
          >
            <CardHeader>
              <CardTitle>Geographic Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Map placeholder - would need actual India map SVG */}
                <div className="lg:col-span-2">
                  <div
                    className={cn(
                      'w-full h-96 rounded-lg border-2 border-dashed flex items-center justify-center',
                      isDark ? 'border-white/20 bg-white/5' : 'border-gray-300 bg-gray-50'
                    )}
                  >
                    <div className="text-center">
                      <div className="text-4xl mb-2">🗺️</div>
                      <p className="text-lg font-medium">India Map</p>
                      <p className="text-sm text-muted-foreground">
                        Interactive map showing state-wise IFSC distribution
                      </p>
                    </div>
                  </div>
                </div>

                {/* State List */}
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  <h4 className="font-medium mb-3">All States</h4>
                  {Array.from(stateData.values())
                    .sort((a, b) => b.count - a.count)
                    .map((state) => (
                      <div
                        key={state.state}
                        className={cn(
                          'p-3 rounded-lg border',
                          isDark ? 'bg-white/5 border-white/10' : 'bg-white border-gray-200'
                        )}
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium">{state.state}</p>
                            <p className="text-sm text-muted-foreground">
                              {state.banks.size} banks
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-blue-500">{state.count}</p>
                            <p className="text-xs text-muted-foreground">IFSCs</p>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </FullWidthSection>

      {/* Common Account Analysis - always show */}
      <FullWidthSection className="space-y-6">
        <div className="space-y-6">
          {/* Complaint Selection */}
          <Card
            className={cn(
              'backdrop-blur-md border shadow-lg',
              isDark ? 'bg-white/10 border-white/10' : 'bg-white/80 border-gray-200/50'
            )}
          >
            <CardHeader>
              <CardTitle>Search Complaints by Number</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 mb-4">
                <div className="flex gap-4">
                  <input
                    type="text"
                    placeholder="Enter complaint numbers (comma-separated)"
                    value={complaintSearchQuery}
                    onChange={(e) => setComplaintSearchQuery(e.target.value)}
                    className={cn(
                      'flex-1 px-3 py-2 border rounded-lg',
                      isDark
                        ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    )}
                  />
                  <Button
                    onClick={() => {
                      const searchNumbers = complaintSearchQuery
                        .split(',')
                        .map((num) => num.trim())
                        .filter((num) => num.length > 0)

                      const matchingComplaints = complaints.filter((complaint) =>
                        searchNumbers.some(
                          (searchNum) =>
                            complaint.title.includes(searchNum) ||
                            (
                              complaint.metadata as { complaint_number?: string }
                            )?.complaint_number?.includes(searchNum)
                        )
                      )

                      setSelectedComplaints(matchingComplaints.map((c) => c.id))
                    }}
                    variant="outline"
                  >
                    Search
                  </Button>
                </div>

                {/* Show selected complaints */}
                {selectedComplaints.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-medium">Selected Complaints:</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedComplaints.map((id) => {
                        const complaint = complaints.find((c) => c.id === id)
                        return complaint ? (
                          <span
                            key={id}
                            className={cn(
                              'px-2 py-1 rounded text-xs',
                              isDark ? 'bg-blue-600 text-white' : 'bg-blue-100 text-blue-800'
                            )}
                          >
                            {complaint.title}
                            <button
                              onClick={() =>
                                setSelectedComplaints((prev) => prev.filter((cId) => cId !== id))
                              }
                              className="ml-1 text-xs hover:text-red-500"
                            >
                              ×
                            </button>
                          </span>
                        ) : null
                      })}
                    </div>
                  </div>
                )}
              </div>
              <div className="flex gap-4">
                <Button
                  onClick={handleAnalyzeCommonAccounts}
                  disabled={selectedComplaints.length < 2 || analysisLoading}
                  className="min-w-[120px]"
                >
                  {analysisLoading ? 'Analyzing...' : 'Analyze Common Accounts'}
                </Button>
                <Button
                  onClick={() => setSelectedComplaints(complaints.map((c) => c.id))}
                  variant="outline"
                >
                  Select All
                </Button>
                <Button onClick={() => setSelectedComplaints([])} variant="outline">
                  Clear All
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Common Accounts Results */}
          {commonAccounts.length > 0 && (
            <Card
              className={cn(
                'backdrop-blur-md border shadow-lg',
                isDark ? 'bg-white/10 border-white/10' : 'bg-white/80 border-gray-200/50'
              )}
            >
              <CardHeader>
                <CardTitle>Common Accounts Found ({commonAccounts.length})</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table
                    className={cn(
                      'w-full border-collapse',
                      isDark ? 'text-white' : 'text-gray-900'
                    )}
                  >
                    <thead>
                      <tr
                        className={cn('border-b', isDark ? 'border-gray-600' : 'border-gray-300')}
                      >
                        <th className="text-left p-3 font-medium">Account Number</th>
                        <th className="text-left p-3 font-medium">Bank</th>
                        <th className="text-left p-3 font-medium">IFSC</th>
                        <th className="text-left p-3 font-medium">Location</th>
                        <th className="text-left p-3 font-medium">Transactions</th>
                        <th className="text-left p-3 font-medium">Complaints</th>
                      </tr>
                    </thead>
                    <tbody>
                      {commonAccounts.map((account) => (
                        <tr
                          key={account.account}
                          className={cn(
                            'border-b hover:bg-black/5 dark:hover:bg-white/5',
                            isDark ? 'border-gray-700' : 'border-gray-200'
                          )}
                        >
                          <td className="p-3">
                            <div className="font-mono text-sm">{account.account}</div>
                          </td>
                          <td className="p-3">
                            <div className="text-sm">{account.bank || 'N/A'}</div>
                          </td>
                          <td className="p-3">
                            <div className="font-mono text-sm">{account.ifsc || 'N/A'}</div>
                          </td>
                          <td className="p-3">
                            <div className="text-sm">
                              {account.location
                                ? `${account.location.city}, ${account.location.state}`
                                : 'N/A'}
                            </div>
                          </td>
                          <td className="p-3">
                            <span className="bg-blue-500 text-white px-2 py-1 rounded text-xs">
                              {account.count}
                            </span>
                          </td>
                          <td className="p-3">
                            <div className="flex flex-wrap gap-1">
                              {account.complaints.map((complaintTitle, i) => (
                                <span
                                  key={i}
                                  className={cn(
                                    'px-2 py-1 rounded text-xs',
                                    isDark ? 'bg-white/10 text-white' : 'bg-gray-200 text-gray-800'
                                  )}
                                >
                                  {complaintTitle}
                                </span>
                              ))}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </FullWidthSection>
    </FullWidthLayout>
  )
}

export default AllStatsPage
