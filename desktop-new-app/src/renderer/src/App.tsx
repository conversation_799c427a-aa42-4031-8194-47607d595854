import React, { useContext } from 'react'
import { AuthContext } from './context/AuthContext'
import { Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom'
import Login from './pages/Login'
import Signup from './pages/Signup'
import VerifyOTP from './pages/VerifyOTP'
import Dashboard from './pages/Dashboard'
import ComplaintDetails from './pages/ComplaintDetails'
import ComplaintSummary from './pages/ComplaintSummary'
import ComplaintUpload from './pages/ComplaintUpload'
import Settings from './pages/Settings'
import Profile from './pages/Profile'
import GraphVisualization from './pages/GraphVisualization'
import InformationSearchPage from './pages/InformationSearchPage'
// AdvancedAnalysisPage removed - I4C portal provides similar functionality

import NoticeGenerationPage from './pages/NoticeGenerationPage'
import StatsPage from './pages/StatsPage'
import AllStatsPage from './pages/AllStatsPage'
import { AuthProvider } from './context/AuthContext.tsx'
import { ThemeProvider } from './context/ThemeContext.tsx'

import LampBackground from './components/LampBackground.tsx'

const App: React.FC = () => {
  return (
    <AuthProvider>
      <ThemeProvider defaultTheme="light" storageKey="vite-ui-theme">
        <AppContent />
      </ThemeProvider>
    </AuthProvider>
  )
}

const AppContent: React.FC = () => {
  const { isAuthenticated, isLoading } = useContext(AuthContext) || {}
  const navigate = useNavigate()
  const location = useLocation()

  const handleLoginSuccess = (): void => {
    navigate('/dashboard')
  }

  // Show nothing until auth check completes
  if (isLoading) {
    return null
  }

  const isAuthPage = ['/login', '/signup', '/verify-otp'].includes(location.pathname)

  return (
    <div className="min-h-screen w-full">
      {isAuthPage ? (
        <main className="w-full">
          <Routes>
            <Route
              path="/login"
              element={
                isAuthenticated ? (
                  <Navigate to="/dashboard" replace />
                ) : (
                  <Login onLoginSuccess={handleLoginSuccess} />
                )
              }
            />
            <Route path="/signup" element={<Signup />} />
            <Route path="/verify-otp" element={<VerifyOTP />} />
          </Routes>
        </main>
      ) : (
        <LampBackground>
          <main className="w-full">
            <Routes>
              <Route
                path="/dashboard"
                element={isAuthenticated ? <Dashboard /> : <Navigate to="/login" replace />}
              />
              <Route
                path="/complaints"
                element={isAuthenticated ? <ComplaintSummary /> : <Navigate to="/login" replace />}
              />
              <Route
                path="/complaint-details/:id"
                element={isAuthenticated ? <ComplaintDetails /> : <Navigate to="/login" replace />}
              />
              <Route
                path="/upload"
                element={isAuthenticated ? <ComplaintUpload /> : <Navigate to="/login" replace />}
              />
              <Route
                path="/graph/:id"
                element={
                  isAuthenticated ? <GraphVisualization /> : <Navigate to="/login" replace />
                }
              />
              <Route
                path="/profile"
                element={isAuthenticated ? <Profile /> : <Navigate to="/login" replace />}
              />
              <Route
                path="/settings"
                element={isAuthenticated ? <Settings /> : <Navigate to="/login" replace />}
              />
              <Route
                path="/information-search"
                element={
                  isAuthenticated ? <InformationSearchPage /> : <Navigate to="/login" replace />
                }
              />

              <Route
                path="/notices/:id"
                element={
                  isAuthenticated ? <NoticeGenerationPage /> : <Navigate to="/login" replace />
                }
              />
              <Route
                path="/stats/:id"
                element={isAuthenticated ? <StatsPage /> : <Navigate to="/login" replace />}
              />
              <Route
                path="/all-stats"
                element={isAuthenticated ? <AllStatsPage /> : <Navigate to="/login" replace />}
              />
              {/* Advanced analysis route removed - I4C portal provides similar functionality */}
              <Route path="*" element={<Navigate to="/login" replace />} />
            </Routes>
          </main>
        </LampBackground>
      )}
    </div>
  )
}

export default App
